@use(Carbon\Carbon)
@use(Plugin\Lottery\Facades\LotterySupport)
@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <style>
        th.sortable { cursor: pointer; position: relative; user-select:none; }
        th.sortable::after {
            content: ""; position: absolute; right: 3px; top: 50%; transform: translateY(-50%);
            font-size: 12px;
        }
        th.sortable.sorted-asc::after  { content: "▲"; }
        th.sortable.sorted-desc::after { content: "▼"; }
        th.sortable.sorted-none::after { content: "↕"; opacity: .35; } /* trạng thái mặc định có thể hiện mũi tên nhạt */


        .table-3-points .fs-4 b {
            font-size: 1.32rem !important;
        }

        /* overlay */
        #tableLoading {
            display: none;
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(255,255,255,0.8);
            justify-content: center;
            align-items: center;
            font-weight: bold;
            z-index: 10;
        }
            #tableLoading .spinner {
            border: 4px solid #eee;
            border-top: 4px solid #333;
            border-radius: 50%;
            width: 20px; height: 20px;
            margin-right: 8px;
            animation: spin 1s linear infinite;
            display:inline-block;
        }
        @keyframes spin { 100% { transform: rotate(360deg); } }

        .cursor-pointer { cursor: pointer; }
    </style>

    <div class="mb-3">
        <div class="row align-items-center">
            @if(isset($cities) && $cities->count() > 1)
                <div class="col-auto">
                    <label class="form-label mb-0">Thành phố:</label>
                    <select class="form-select" id="citySelect" style="width: auto; display: inline-block;">
                        @foreach($cities as $cityOption)
                            <option value="{{ $cityOption->id }}" {{ $city->id == $cityOption->id ? 'selected' : '' }}>
                                {{ $cityOption->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
            @endif
        </div>
    </div>

    @if(isset($found) && !empty($found))
        @php
            $items = $numbers->where('date', $rangeEnd->toDateString());
        @endphp

        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Kết quả xổ số {{ $city->name }} ngày {{ $rangeEnd->format('d/m/Y') }}</h5>
                    </div>
                    <div class="card-body">
                        @if($items->isNotEmpty())
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped table-3-points">
                                    <tbody>
                                        @php
                                            $iii = 0;
                                        @endphp
                                        @foreach (LotterySupport::getPrizeEnums((string) $city->region) as $prizeKey => $prizeLabel)
                                            @php
                                                $prizes = $items->where('level', $prizeKey);
                                            @endphp
                                            <tr>
                                                <td>{{ $prizeLabel }}</td>
                                                <td class="text-center">
                                                    @php
                                                        $levels = $items->where('level', $prizeKey);
                                                    @endphp
                                                    <div
                                                        @class([
                                                            'row justify-content-center',
                                                            'row-cols-3' => $levels->count() > 5,
                                                        ])>
                                                        @foreach ($levels as $item)
                                                            <div
                                                                data-index="{{ $loop->index }}"
                                                                class="col fs-4 fw-bold prize-{{ $prizeKey }} 3-point-prize"
                                                                data-last_two_digits="{{ $item->last_two_digits }}">
                                                                <div class="d-flex justify-content-center">
                                                                    @php
                                                                        $iii++;
                                                                        $number = $item->number;
                                                                        $splits = str_split($number);
                                                                    @endphp
                                                                    @foreach($splits as $digit)
                                                                        <b class="mx-1 text-danger">{{ $digit }}</b>
                                                                    @endforeach
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <p class="text-muted">Không có dữ liệu cho ngày {{ $rangeEnd->format('d/m/Y') }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif

    <div class="block data-append-form">
        <div class="block-main-heading">
            <h2 class="fs-5 mb-0">Chi tiết thống kê {{ PageTitle::getTitle(false) }}</h2>
        </div>
        @php
            $quantity = collect($found)->max(fn ($items) => max(array_keys($items))) ?? 10;
            $dateNumbers = $numbers->groupBy('date');
            $firstNumbers = $numbersGrouped->first() ?? collect();
            $lastNumbers = $numbersGrouped->last() ?? $firstNumbers;
            $numberQuantity = $firstNumbers->isNotEmpty() ? $firstNumbers->sum(fn ($item) => strlen($item->number ?? '')) : 27;
            
            // Tạo mapping ID -> Số từ bảng hiển thị
            $idToNumber = [];
            $idx = 0;
            foreach ($firstNumbers as $item) {
                foreach ($item->splits ?? [] as $num) {
                    $idx++;
                    $idToNumber[$idx] = $num;
                }
            }
        @endphp

        <script>
            window.tableData = {
                found: @json($found),
                idToNumber: @json($idToNumber),
                quantity: {{ $quantity }},
                numberQuantity: {{ $numberQuantity }}
            };
        </script>

        <div class="block-main-content position-relative">
            <table id="statTable" class="table table-bordered table-striped table-3-points">
                <thead>
                    <tr>
                        <th rowspan="2">
                            <span>ID</span>
                            <div>
                                <select name="id_number_1">
                                    <option value=""></option>
                                    @for ($i = 1; $i < $numberQuantity; $i++)
                                        <option value="{{ $i }}">{{ $i }}</option>
                                    @endfor
                                </select>
                                <select name="id_number_2">
                                    <option value=""></option>
                                    @for ($i = 2; $i <= $numberQuantity; $i++)
                                        <option value="{{ $i }}">{{ $i }}</option>
                                    @endfor
                                </select>
                                <select name="id_number_3">
                                    <option value=""></option>
                                    @for ($i = 3; $i <= $numberQuantity; $i++)
                                        <option value="{{ $i }}">{{ $i }}</option>
                                    @endfor
                                </select>
                            </div>
                        </th>
                        <th rowspan="2">
                            <span>Số tương ứng</span>
                        </th>
                        <th rowspan="2" class="sortable" data-col="total">
                            <span>Số lần</span>
                        </th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                    {{-- Render bằng JS --}}
                </tbody>
            </table>

            <!-- overlay loading -->
            <div id="tableLoading">
                <div class="spinner"></div> Đang tải...
            </div>
        </div>

        {{-- Pagination render by JS --}}
        <div class="mt-3" id="paginationContainer"></div>

        @php
            $items = $firstNumbers;
        @endphp

        @if ($firstNumbers->isNotEmpty())
            <div class="block-main-content">
                <table class="table table-bordered table-striped table-3-points">
                    <tbody>
                        @php
                            $iii = 0;
                        @endphp
                        @foreach (LotterySupport::getPrizeEnums((string) $city->region) as $prizeKey => $prizeLabel)
                            @php
                                $prizes = $items->where('level', $prizeKey);
                            @endphp
                            <tr>
                                <td>{{ $prizeLabel }}</td>
                                <td class="text-center">
                                    @php
                                        $levels = $items->where('level', $prizeKey);
                                    @endphp
                                    <div
                                        @class([
                                            'row justify-content-center',
                                            'row-cols-3' => $levels->count() > 5,
                                        ])>
                                        @foreach ($levels as $item)
                                            <div
                                                data-index="{{ $loop->index }}"
                                                class="col fs-4 fw-bold prize-{{ $prizeKey }} 3-point-prize"
                                                data-last_two_digits="{{ $item->last_two_digits }}">
                                                <div class="d-flex justify-content-center">
                                                    @foreach ($item->splits as $k => $num)
                                                        <b title="{{ $city->region . '-' . ++$iii . '-' . $item->date}}"
                                                            data-key="{{ $iii }}"
                                                            data-num="{{ $num }}"
                                                            style="cursor: pointer"
                                                            @class([
                                                                'tkcault',
                                                                'setlotocolor',
                                                                'last_two_digits' => $loop->last || $loop->index === $loop->count - 2
                                                            ])>{{ $num }}</b>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif
    </div>
@endsection

@push('footer')
    <script>
        // Client-side Pagination Manager
        class TablePagination {
            constructor(data, idToNumber, quantity) {
                console.log(data);
                this.allData = Object.entries(data); // Convert object to array of [key, value]
                this.filteredData = [...this.allData];
                this.idToNumber = idToNumber;
                this.quantity = quantity;
                this.currentPage = 1;
                this.perPage = parseInt(document.getElementById('perPageSelect')?.value) || 50;
                this.sortState = { col: null, dir: 'none' };
                this.filterState = { num1: '', num2: '', num3: '' };
            }

            get totalItems() {
                return this.filteredData.length;
            }

            get totalPages() {
                return Math.ceil(this.totalItems / this.perPage);
            }

            get pageData() {
                const start = (this.currentPage - 1) * this.perPage;
                const end = start + this.perPage;
                return this.filteredData.slice(start, end);
            }

            applyFilter(num1, num2, num3) {
                this.filterState = { num1, num2, num3 };

                if (!num1 && !num2 && !num3) {
                    this.filteredData = [...this.allData];
                } else {
                    this.filteredData = this.allData.filter(([xkey]) => {
                        const keys = xkey.split('-');
                        let match = true;

                        if (num1) match = match && keys[0] == num1;
                        if (num2) match = match && keys[1] == num2;
                        if (num3) match = match && keys[2] == num3;

                        return match;
                    });
                }

                this.currentPage = 1;
                this.render();
            }

            calculateTotal(items) {
                return Object.keys(items)[0];
            }

            calculateRate(items) {
                let sum = 0;
                for (let i = 1; i <= this.quantity; i++) {
                    sum += items[i]?.count || 0;
                }
                const total = this.calculateTotal(items);
                if (total === 0) return 0;
                return 1 - (sum / total);
            }

            applySort(colIdx, dir) {
                this.sortState = { col: colIdx, dir };

                if (dir === 'none') {
                    // Reset to original order
                    this.filteredData.sort((a, b) => {
                        return this.allData.indexOf(a) - this.allData.indexOf(b);
                    });
                } else {
                    this.filteredData.sort((a, b) => {
                        let valA, valB;

                        if (colIdx === 'total') {
                            valA = this.calculateTotal(a[1]);
                            valB = this.calculateTotal(b[1]);
                        } else if (colIdx === 'rate') {
                            valA = this.calculateRate(a[1]);
                            valB = this.calculateRate(b[1]);
                        } else {
                            valA = a[1][colIdx - 1]?.count || 0;
                            valB = b[1][colIdx - 1]?.count || 0;
                        }

                        return dir === 'asc' ? valA - valB : valB - valA;
                    });
                }

                this.render();
            }

            renderRows() {
                const tbody = document.getElementById('tableBody');
                const fragment = document.createDocumentFragment();

                this.pageData.forEach(([xkey, items]) => {
                    const keys = xkey.split('-');
                    const num1 = this.idToNumber[keys[0]] || '';
                    const num2 = this.idToNumber[keys[1]] || '';
                    const num3 = this.idToNumber[keys[2]] || '';

                    const row = document.createElement('tr');

                    // ID column
                    const idCell = document.createElement('td');
                    idCell.textContent = xkey;
                    row.appendChild(idCell);

                    // Numbers column
                    const numCell = document.createElement('td');
                    numCell.textContent = `${num1}-${num2}-${num3}`;
                    row.appendChild(numCell);

                    // Total column
                    const totalCell = document.createElement('td');
                    totalCell.textContent = this.calculateTotal(items);
                    row.appendChild(totalCell);

                    fragment.appendChild(row);
                });

                tbody.innerHTML = '';
                tbody.appendChild(fragment);
            }

            render() {
                this.renderRows();
                this.renderPagination();
            }

            renderPagination() {
                const container = document.getElementById('paginationContainer');
                if (!container) return;

                if (this.totalPages <= 1) {
                    container.innerHTML = '';
                    return;
                }

                let html = '<nav><ul class="pagination justify-content-center">';

                // Previous button
                html += `<li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${this.currentPage - 1}">Previous</a>
                </li>`;

                // Page numbers
                const startPage = Math.max(1, this.currentPage - 2);
                const endPage = Math.min(this.totalPages, this.currentPage + 2);

                for (let i = startPage; i <= endPage; i++) {
                    html += `<li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>`;
                }

                // Next button
                html += `<li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${this.currentPage + 1}">Next</a>
                </li>`;

                html += '</ul></nav>';
                container.innerHTML = html;

                // Add click handlers
                container.querySelectorAll('.page-link').forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        const page = parseInt(e.target.dataset.page);
                        if (page && page !== this.currentPage && page >= 1 && page <= this.totalPages) {
                            this.currentPage = page;
                            this.render();
                        }
                    });
                });
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            const { found, idToNumber, quantity, numberQuantity } = window.tableData;
            const pagination = new TablePagination(found, idToNumber, quantity);

            // Initial render
            pagination.render();

            // Filter handlers
            const num1Select = document.querySelector('select[name="id_number_1"]');
            const num2Select = document.querySelector('select[name="id_number_2"]');
            const num3Select = document.querySelector('select[name="id_number_3"]');

            function applyFilters() {
                const num1 = num1Select.value;
                const num2 = num2Select.value;
                const num3 = num3Select.value;
                pagination.applyFilter(num1, num2, num3);
            }

            if (num1Select) num1Select.addEventListener('change', applyFilters);
            if (num2Select) num2Select.addEventListener('change', applyFilters);
            if (num3Select) num3Select.addEventListener('change', applyFilters);

            // Sort handlers
            document.querySelectorAll('th.sortable').forEach(th => {
                th.addEventListener('click', function() {
                    const col = this.dataset.col;
                    let newDir = 'asc';

                    if (this.classList.contains('sorted-asc')) {
                        newDir = 'desc';
                    } else if (this.classList.contains('sorted-desc')) {
                        newDir = 'none';
                    }

                    // Remove all sort classes
                    document.querySelectorAll('th.sortable').forEach(h => {
                        h.classList.remove('sorted-asc', 'sorted-desc', 'sorted-none');
                    });

                    // Add new sort class
                    this.classList.add(`sorted-${newDir}`);

                    pagination.applySort(col, newDir);
                });
            });
        });
    </script>
@endpush
