<?php

namespace Plugin\Lottery\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Plugin\Lottery\Enums\LotteryRegionEnum;
use Plugin\Lottery\Facades\LotteryStatistic;
use Plugin\Lottery\Facades\LotterySupport;
use Plugin\Lottery\Models\LotteryCityDate;
use Plugin\Lottery\Models\LotteryStatistic as LotteryStatisticModel;

class LotteryStatisticController extends BaseController
{
    public function index()
    {
        $this->pageTitle('Thống kê');

        return view('plugins/lottery::statistics.index');
    }

    public function twoPointUniqueMB()
    {
        $this
            ->breadcrumb()
            ->add(trans('plugins/lottery::lottery.statistic.name'));

        $this->pageTitle('2 điểm độc nhất miền bắc');

        $region = LotteryRegionEnum::MB;
        $type = 'vip';

        $dayOfWeek = LotteryStatistic::getRequestDayOfWeek();

        $cities = LotterySupport::getCitiesByRegion($region, $dayOfWeek);

        // Handle city_id parameter for city selection
        $cityId = request()->input('city_id');
        if ($cityId) {
            $selectedCity = $cities->where('id', $cityId)->first();
            $city = $selectedCity ?: $cities->first();
        } else {
            $city = $cities->first();
        }

        // Lấy range từ request (số ngày)
        $rangeInput = request()->input('range', 30);
        $rangeEnd = Carbon::yesterday(); // Dữ liệu đến hôm qua

        // Lấy option cache (mặc định bật)
        $useCache = true;

        // Xác định strategy: trực tiếp từ DB hay từ statistics
        // Cân bằng giữa độ chính xác và hiệu suất:
        // - DB trực tiếp: range <= 120 ngày (chính xác cao, nhanh)
        // - Statistics: range > 120 ngày (tiết kiệm memory, tránh timeout)
        if ($rangeInput === 'all') {
            $range = null; // Không giới hạn - lấy tất cả
            $useStatistics = true;
            set_time_limit(120); // 2 phút cho range=all (yearly stats nhanh hơn)
            ini_set('memory_limit', '256M'); // Giảm memory limit
        } else {
            $range = (int) $rangeInput;
            // Threshold 120 ngày - dùng statistics cho range >= 180 để tránh timeout
            $useStatistics = $range > 120;
        }

        if ($useStatistics) {
            // Lấy từ statistics đã tổng hợp
            $result = $this->getFromStatistics($city, $range, $rangeEnd, $rangeInput);
        } else {
            // Lấy trực tiếp từ database với option cache
            $result = $this->getFromDatabase($city, $range, $rangeEnd, $type, $useCache);
        }

        $data = array_merge($result, [
            'city' => $city,
            'cities' => $cities,
            'range' => $rangeInput === 'all' ? 'all' : $range,
            'rangeEnd' => $rangeEnd,
            'useStatistics' => $useStatistics,
            'useCache' => $useCache,
        ]);

        return view('plugins/lottery::statistics.two-point-unique-mb', $data);
    }

    public function twoPointTodayMB()
    {
        $this
            ->breadcrumb()
            ->add(trans('plugins/lottery::lottery.statistic.name'));

        $this->pageTitle('Cầu chạy hôm nay Miền Bắc');

        $region = LotteryRegionEnum::MB;
        $type = 'two-point';

        $dayOfWeek = LotteryStatistic::getRequestDayOfWeek();

        $cities = LotterySupport::getCitiesByRegion($region, $dayOfWeek);

        if ($region == LotteryRegionEnum::MB) {
            $city = $cities->first();
        }

        // Lấy range từ request (số ngày)
        $rangeInput = 60;
        $rangeEnd = Carbon::yesterday(); // Dữ liệu đến hôm qua

        // Lấy option cache (mặc định bật)
        $useCache = false;

        if ($rangeInput === 'all') {
            $range = null; // Không giới hạn - lấy tất cả
            $useStatistics = true;
            set_time_limit(120); // 2 phút cho range=all (yearly stats nhanh hơn)
            ini_set('memory_limit', '256M'); // Giảm memory limit
        } else {
            $range = (int) $rangeInput;
            // Threshold 120 ngày - dùng statistics cho range >= 180 để tránh timeout
            $useStatistics = $range > 120;
        }

        if ($useStatistics) {
            $result = $this->getFromStatistics($city, $range, $rangeEnd, $rangeInput);
        } else {
            $result = $this->getFromTodayDatabase($city, $range, $rangeEnd, $type, $useCache);
        }

        $data = array_merge($result, [
            'city' => $city,
            'range' => $rangeInput === 'all' ? 'all' : $range,
            'rangeEnd' => $rangeEnd,
            'useStatistics' => $useStatistics,
            'useCache' => $useCache,
        ]);

        return view('plugins/lottery::statistics.two-point-today-mb', $data);
    }

    /**
     * Lấy dữ liệu trực tiếp từ database (cho range ≤ 60 ngày)
     */
    protected function getFromDatabase($city, $range, $rangeEnd, $type, $useCache = true)
    {
        $rangeStart = $rangeEnd->copy()->subDays($range - 1);

        // Tạo cache key dựa trên các tham số
        $cacheKey = sprintf(
            'two_point_unique_mb_db_%s_%d_%s_%s',
            $city->id,
            $range,
            $rangeEnd->toDateString(),
            $type
        );

        // Nếu bật cache, thử lấy từ cache trước
        if ($useCache) {
            $cached = Cache::get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }

        // Nếu không có cache hoặc tắt cache, query từ database
        $cityDates = LotteryCityDate::query()
            ->where('city_id', $city->id)
            ->whereBetween('date', [$rangeStart->toDateString(), $rangeEnd->toDateString()])
            ->oldest('date')
            ->getQuery()
            ->get();

        $numbers = LotteryStatistic::getNumbersForRange($city, $rangeStart, $rangeEnd);

        if ($numbers->isEmpty()) {
            $result = [
                'found' => [],
                'numbersGrouped' => collect(),
                'cityDates' => $cityDates,
                'numbers' => collect(),
                'combination' => [],
                'rangeStart' => $rangeStart,
            ];

            // Lưu cache nếu bật (cache 24 giờ)
            if ($useCache) {
                Cache::put($cacheKey, $result, now()->addHours(24));
            }

            return $result;
        }

        [$found, $numbersGrouped, $combinations] = LotteryStatistic::twoPointUniqueVipMB($city, $numbers, $type);

        $result = [
            'found' => $found,
            'numbersGrouped' => $numbersGrouped,
            'cityDates' => $cityDates,
            'numbers' => $numbers,
            'combination' => $combinations->last(),
            'rangeStart' => $rangeStart,
        ];

        // Lưu cache nếu bật (cache 24 giờ)
        if ($useCache) {
            Cache::put($cacheKey, $result, now()->addHours(24));
        }

        return $result;
    }

    /**
     * Lấy dữ liệu từ statistics đã tổng hợp với logic merge thông minh (cho range > 365 ngày)
     */
    protected function getFromStatistics($city, $range, $rangeEnd, $rangeInput = null)
    {
        // Tạo cache key
        $cacheKey = sprintf(
            'two_point_unique_mb_stats_%s_%s_%s',
            $city->id,
            $range ?? 'all',
            $rangeEnd->toDateString()
        );

        // Thử lấy từ cache (TTL 24 giờ cho range lớn)
        $cached = Cache::get($cacheKey);
        if ($cached !== null) {
            return $cached;
        }

        // range=all: lấy tất cả dữ liệu có trong DB
        // range cụ thể: tính từ rangeEnd ngược lại range ngày
        if ($rangeInput === 'all') {
            // Lấy ngày đầu tiên từ DB
            $firstDate = DB::table('lt_mb_results')
                ->where('city_id', $city->id)
                ->orderBy('date', 'asc')
                ->limit(1)
                ->value('date');

            $rangeStart = $firstDate ? Carbon::parse($firstDate) : $rangeEnd->copy()->subYear();
        } else {
            $rangeStart = $rangeEnd->copy()->subDays($range - 1);
        }

        // ƯU TIÊN SỬ DỤNG YEARLY STATISTICS cho range lớn
        // CHỈ dùng yearly khi range bao phủ ít nhất 1 năm HOÀN CHỈNH (365+ ngày)
        // Vì yearly chứa dữ liệu cả năm, không thể dùng cho partial year
        $shouldUseYearly = $rangeInput === 'all' || ($range && $range >= 365);

        if ($shouldUseYearly) {
            $result = $this->getFromYearlyStatistics($city, $rangeStart, $rangeEnd, $rangeInput);
            if ($result !== null) {
                // Cache kết quả (TTL 24 giờ)
                Cache::put($cacheKey, $result, now()->addHours(24));

                return $result;
            }
            // Nếu không có yearly stats, fallback sang monthly
        }

        // Lấy statistics từ database - CHỈ LẤY RECORD CUỐI CÙNG CỦA MỖI THÁNG
        // Vì command chạy hàng ngày tạo nhiều record cho cùng 1 tháng
        $startMonth = $rangeStart->copy()->startOfMonth()->endOfMonth()->toDateString();
        $endMonth = $rangeEnd->toDateString();

        // Query TRƯỚC để đếm số records, nếu quá nhiều thì dùng yearly fallback
        $totalMonthlyRecords = LotteryStatisticModel::query()
            ->where('type', 'two_point_unique_vip_mb_monthly')
            ->where('region', $city->region)
            ->where('city_id', $city->getKey())
            ->where('latest_date', '>=', $startMonth)
            ->where('latest_date', '<=', $endMonth)
            ->count();

        // Nếu có quá nhiều monthly records (> 30), dùng yearly luôn
        if ($totalMonthlyRecords > 30) {
            $result = $this->getFromYearlyStatistics($city, $rangeStart, $rangeEnd, $rangeInput);
            if ($result !== null) {
                Cache::put($cacheKey, $result, now()->addHours(24));

                return $result;
            }
        }

        // Query tất cả records trong khoảng thời gian
        $merged = [];
        $lastCombination = [];
        $latestDate = null;
        $latestPayload = null;
        $processedMonths = [];
        $recordCount = 0;

        // Nếu ít records (< 50), query 1 lần. Nếu nhiều, dùng chunk
        if ($totalMonthlyRecords < 50) {
            // Query trực tiếp cho performance tốt hơn
            $allStats = LotteryStatisticModel::query()
                ->select('latest_date', 'payload')
                ->where('type', 'two_point_unique_vip_mb_monthly')
                ->where('region', $city->region)
                ->where('city_id', $city->getKey())
                ->where('latest_date', '>=', $startMonth)
                ->where('latest_date', '<=', $endMonth)
                ->orderBy('latest_date', 'desc')
                ->get();

            foreach ($allStats as $stat) {
                $statDate = Carbon::parse($stat->latest_date);
                $monthKey = $statDate->format('Y-m');

                // Chỉ lấy record đầu tiên (mới nhất) của mỗi tháng
                if (isset($processedMonths[$monthKey])) {
                    continue;
                }

                $processedMonths[$monthKey] = true;
                $recordCount++;

                $payload = $stat->payload;
                $monthStats = $payload['statistics'] ?? [];
                $lastCombination = $payload['combination'] ?? [];

                if (! $latestDate || $stat->latest_date > $latestDate) {
                    $latestDate = $stat->latest_date;
                    $latestPayload = $payload;
                }

                // SIMPLE MERGE: Chỉ cộng dồn các streak
                foreach ($monthStats as $pairKey => $streaks) {
                    if (! isset($merged[$pairKey])) {
                        $merged[$pairKey] = [];
                    }

                    foreach ($streaks as $streakLen => $data) {
                        if (! isset($merged[$pairKey][$streakLen])) {
                            $merged[$pairKey][$streakLen] = ['count' => 0, 'countx' => 0];
                        }
                        $merged[$pairKey][$streakLen]['count'] += $data['count'] ?? 0;
                        $merged[$pairKey][$streakLen]['countx'] += $data['countx'] ?? 0;
                    }
                }

                unset($payload, $monthStats);
            }
        } else {
            // Chunk 20 records mỗi lần cho số lượng lớn
            LotteryStatisticModel::query()
                ->select('latest_date', 'payload')
                ->where('type', 'two_point_unique_vip_mb_monthly')
                ->where('region', $city->region)
                ->where('city_id', $city->getKey())
                ->where('latest_date', '>=', $startMonth)
                ->where('latest_date', '<=', $endMonth)
                ->orderBy('latest_date', 'desc')
                ->chunk(20, function ($stats) use (&$merged, &$lastCombination, &$latestDate, &$latestPayload, &$processedMonths, &$recordCount) {
                    foreach ($stats as $stat) {
                        $statDate = Carbon::parse($stat->latest_date);
                        $monthKey = $statDate->format('Y-m');

                        // Chỉ lấy record đầu tiên (mới nhất) của mỗi tháng
                        if (isset($processedMonths[$monthKey])) {
                            continue;
                        }

                        $processedMonths[$monthKey] = true;
                        $recordCount++;

                        $payload = $stat->payload;
                        $monthStats = $payload['statistics'] ?? [];
                        $lastCombination = $payload['combination'] ?? [];

                        // Track latest for sample numbers
                        if (! $latestDate || $stat->latest_date > $latestDate) {
                            $latestDate = $stat->latest_date;
                            $latestPayload = $payload;
                        }

                        // SIMPLE MERGE: Chỉ cộng dồn các streak, không filter
                        foreach ($monthStats as $pairKey => $streaks) {
                            if (! isset($merged[$pairKey])) {
                                $merged[$pairKey] = [];
                            }

                            foreach ($streaks as $streakLen => $data) {
                                if (! isset($merged[$pairKey][$streakLen])) {
                                    $merged[$pairKey][$streakLen] = ['count' => 0, 'countx' => 0];
                                }
                                $merged[$pairKey][$streakLen]['count'] += $data['count'] ?? 0;
                                $merged[$pairKey][$streakLen]['countx'] += $data['countx'] ?? 0;
                            }
                        }

                        unset($payload, $monthStats);
                    }
                });
        }

        if ($recordCount === 0) {
            $result = [
                'found' => [],
                'numbersGrouped' => collect(),
                'cityDates' => collect(),
                'numbers' => collect(),
                'combination' => [],
                'rangeStart' => $rangeStart,
                'monthsUsed' => 0,
            ];

            // Cache kết quả rỗng
            Cache::put($cacheKey, $result, now()->addHours(24));

            return $result;
        }

        // Lấy một số numbers mẫu để hiển thị (từ tháng gần nhất)
        $lastNumbers = $latestPayload['last_numbers'] ?? [];

        // Convert array to collection of objects nếu cần
        $sampleNumbers = collect($lastNumbers)->map(function ($item) {
            if (is_array($item)) {
                return (object) $item;
            }

            return $item;
        });

        $result = [
            'found' => $merged,
            'numbersGrouped' => collect([$rangeEnd->toDateString() => $sampleNumbers]),
            'cityDates' => collect(),
            'numbers' => $sampleNumbers,
            'combination' => $lastCombination,
            'rangeStart' => $rangeStart,
            'monthsUsed' => $recordCount,
        ];

        // Cache kết quả (TTL 24 giờ cho range lớn, đặc biệt range=all)
        Cache::put($cacheKey, $result, now()->addHours(24));

        return $result;
    }

    /**
     * Lấy dữ liệu từ yearly statistics (tiết kiệm memory cho range lớn)
     * CHỈ dùng khi range bao phủ TOÀN BỘ các năm trong khoảng
     */
    protected function getFromYearlyStatistics($city, $rangeStart, $rangeEnd, $rangeInput)
    {
        $startYear = $rangeStart->year;
        $endYear = $rangeEnd->year;

        // Kiểm tra xem có phải full years không
        // Nếu range bắt đầu không phải 01/01 hoặc kết thúc không phải 31/12 → không dùng yearly
        $isStartOfYear = $rangeStart->month == 1 && $rangeStart->day == 1;
        $isEndOfYear = $rangeEnd->month == 12 && $rangeEnd->day >= 28; // Gần cuối năm

        // CHỈ dùng yearly nếu:
        // 1. range=all, HOẶC
        // 2. Bắt đầu từ đầu năm VÀ (kết thúc cuối năm HOẶC năm khác nhau >= 2)
        $canUseYearly = $rangeInput === 'all' ||
                       ($isStartOfYear && ($isEndOfYear || ($endYear - $startYear) >= 2));

        if (! $canUseYearly) {
            // Không dùng yearly cho partial year, return null để dùng monthly
            return null;
        }

        // Query yearly statistics trong khoảng thời gian
        $yearlyStats = LotteryStatisticModel::query()
            ->select('latest_date', 'payload')
            ->where('type', 'two_point_unique_vip_mb_yearly')
            ->where('region', $city->region)
            ->where('city_id', $city->getKey())
            ->whereYear('latest_date', '>=', $startYear)
            ->whereYear('latest_date', '<=', $endYear)
            ->orderBy('latest_date', 'asc')
            ->get();

        if ($yearlyStats->isEmpty()) {
            // Không có yearly stats, return null để fallback sang monthly
            return null;
        }

        // Merge statistics từ các năm
        $merged = [];
        $lastCombination = [];
        $latestDate = null;
        $latestPayload = null;

        foreach ($yearlyStats as $stat) {
            $payload = $stat->payload;
            $yearStats = $payload['statistics'] ?? [];
            $lastCombination = $payload['combination'] ?? [];

            if (! $latestDate || $stat->latest_date > $latestDate) {
                $latestDate = $stat->latest_date;
                $latestPayload = $payload;
            }

            // Simple merge: cộng dồn các streak
            foreach ($yearStats as $pairKey => $streaks) {
                if (! isset($merged[$pairKey])) {
                    $merged[$pairKey] = [];
                }

                foreach ($streaks as $streakLen => $data) {
                    if (! isset($merged[$pairKey][$streakLen])) {
                        $merged[$pairKey][$streakLen] = ['count' => 0, 'countx' => 0];
                    }
                    $merged[$pairKey][$streakLen]['count'] += $data['count'] ?? 0;
                    $merged[$pairKey][$streakLen]['countx'] += $data['countx'] ?? 0;
                }
            }
        }

        $lastNumbers = $latestPayload['last_numbers'] ?? [];

        $sampleNumbers = collect($lastNumbers)->map(function ($item) {
            return is_array($item) ? (object) $item : $item;
        });

        return [
            'found' => $merged,
            'numbersGrouped' => collect([$rangeEnd->toDateString() => $sampleNumbers]),
            'cityDates' => collect(),
            'numbers' => $sampleNumbers,
            'combination' => $lastCombination,
            'rangeStart' => $rangeStart,
            'monthsUsed' => $yearlyStats->count() * 12, // Ước tính
            'yearsUsed' => $yearlyStats->count(),
        ];
    }

    protected function mergeMonthlyStatistics(array $current, array $previous): array
    {
        if (empty($previous)) {
            return $current;
        }

        $allKeys = array_unique(array_merge(array_keys($current), array_keys($previous)));

        $merged = [];

        foreach ($allKeys as $pairKey) {
            $currentStreaks = $current[$pairKey] ?? [];
            $previousStreaks = $previous[$pairKey] ?? [];

            // Instead of simple addition, we need to be more careful about merging
            // to avoid streak accumulation that exceeds logical limits
            foreach ($previousStreaks as $streak => $stats) {
                if (isset($currentStreaks[$streak])) {
                    // If same streak exists in both, add them
                    $currentStreaks[$streak]['count'] = ($currentStreaks[$streak]['count'] ?? 0) + ($stats['count'] ?? 0);
                    $currentStreaks[$streak]['countx'] = ($currentStreaks[$streak]['countx'] ?? 0) + ($stats['countx'] ?? 0);
                } else {
                    // If streak only exists in previous month, include it
                    $currentStreaks[$streak] = $stats;
                }
            }

            ksort($currentStreaks, SORT_NUMERIC);

            $merged[$pairKey] = $currentStreaks;
        }

        return $merged;
    }

    protected function getFromTodayDatabase($city, $range, $rangeEnd, $type, $useCache = true)
    {
        $rangeStart = $rangeEnd->copy()->subDays($range - 1);

        // Tạo cache key dựa trên các tham số
        $cacheKey = sprintf(
            'two_point_today_mb_db_%s_%d_%s_%s',
            $city->id,
            $range,
            $rangeEnd->toDateString(),
            $type
        );

        // Nếu bật cache, thử lấy từ cache trước
        if ($useCache) {
            $cached = Cache::get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }

        // Nếu không có cache hoặc tắt cache, query từ database
        $cityDates = LotteryCityDate::query()
            ->where('city_id', $city->id)
            ->whereBetween('date', [$rangeStart->toDateString(), $rangeEnd->toDateString()])
            ->oldest('date')
            ->getQuery()
            ->get();

        $numbers = LotteryStatistic::getNumbersForRange($city, $rangeStart, $rangeEnd, 'latest');

        if ($numbers->isEmpty()) {
            $result = [
                'found' => [],
                'numbersGrouped' => collect(),
                'cityDates' => $cityDates,
                'numbers' => collect(),
                'combination' => [],
                'rangeStart' => $rangeStart,
            ];

            // Lưu cache nếu bật (cache 24 giờ)
            if ($useCache) {
                Cache::put($cacheKey, $result, now()->addHours(24));
            }

            return $result;
        }

        [$found, $numbersGrouped, $combinations] = LotteryStatistic::twoPointTodayVipMB($city, $numbers, $type);

        $result = [
            'found' => $found,
            'numbersGrouped' => $numbersGrouped,
            'cityDates' => $cityDates,
            'numbers' => $numbers,
            'combination' => $combinations->last(),
            'rangeStart' => $rangeStart,
        ];

        // Lưu cache nếu bật (cache 24 giờ)
        if ($useCache) {
            Cache::put($cacheKey, $result, now()->addHours(24));
        }

        return $result;
    }

    public function special($type, $region, Request $request)
    {
        $this
            ->breadcrumb()
            ->add(trans('plugins/lottery::lottery.statistic.name'));

        $regionName = LotterySupport::getRegionName($region);
        
        $type = match ($type) {
            'duoi' => 'last',
            'tong-dau' => 'total-first',
            'tong-duoi' => 'total-last',
            'dau' => 'first',
            default => $type,
        };

        $typeTitle = match ($type) {
            'last' => 'Đuôi',
            'total-first' => 'Tổng đầu',
            'total-last' => 'Tổng đuôi',
            'first' => 'Đầu',
            default => 'Đầu',
        };

        $pageTitle = __('Cầu đặc biệt :type :region', [
            'type' => $typeTitle,
            'region' => $regionName,
        ]);
        $this->pageTitle($pageTitle);

        $cities = LotterySupport::getCitiesByRegion($region);

        $city = null;
        if ($region == LotteryRegionEnum::MB) {
            $city = $cities->first();
        } else {
            $cities = $cities->whereNotNull('parent_id');
            if ($cityId = $request->input('city_id')) {
                $city = $cities->firstWhere('id', $cityId);
            }

            if (! $city) {
                $city = $cities->first();
            }
        }

        // Lấy range từ request (số ngày)
        $rangeInput = 60;
        $rangeEnd = Carbon::yesterday(); // Dữ liệu đến hôm qua

        // Lấy option cache (mặc định bật)
        $useCache = false;

        if ($rangeInput === 'all') {
            $range = null; // Không giới hạn - lấy tất cả
            $useStatistics = true;
            set_time_limit(120); // 2 phút cho range=all (yearly stats nhanh hơn)
            ini_set('memory_limit', '256M'); // Giảm memory limit
        } else {
            $range = (int) $rangeInput;
            // Threshold 120 ngày - dùng statistics cho range >= 180 để tránh timeout
            $useStatistics = $range > 120;
        }

        if ($useStatistics) {
            $result = $this->getFromStatistics($city, $range, $rangeEnd, $rangeInput);
        } else {
            $result = $this->getFromSpecialDatabase($city, $range, $rangeEnd, $type, $useCache);
        }

        $data = array_merge($result, [
            'city' => $city,
            'range' => $rangeInput === 'all' ? 'all' : $range,
            'rangeEnd' => $rangeEnd,
            'useStatistics' => $useStatistics,
            'useCache' => $useCache,
            'cities' => $cities,
        ]);

        return view('plugins/lottery::statistics.two-point-today-mb', $data);
    }

    protected function getFromSpecialDatabase($city, $range, $rangeEnd, $type, $useCache = true)
    {
        $rangeStart = $rangeEnd->copy()->subDays($range - 1);

        // Tạo cache key dựa trên các tham số
        $cacheKey = sprintf(
            'statistics_special_last_%s_db_%s_%d_%s_%s',
            $city->region,
            $city->id,
            $range,
            $rangeEnd->toDateString(),
            $type
        );

        // Nếu bật cache, thử lấy từ cache trước
        if ($useCache) {
            $cached = Cache::get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }

        // Nếu không có cache hoặc tắt cache, query từ database
        $cityDates = LotteryCityDate::query()
            ->where('city_id', $city->id)
            ->whereBetween('date', [$rangeStart->toDateString(), $rangeEnd->toDateString()])
            ->oldest('date')
            ->getQuery()
            ->get();

        $numbers = LotteryStatistic::getNumbersForRange($city, $rangeStart, $rangeEnd, 'latest');

        if ($numbers->isEmpty()) {
            $result = [
                'found' => [],
                'numbersGrouped' => collect(),
                'cityDates' => $cityDates,
                'numbers' => collect(),
                'combination' => [],
                'rangeStart' => $rangeStart,
            ];

            // Lưu cache nếu bật (cache 24 giờ)
            if ($useCache) {
                Cache::put($cacheKey, $result, now()->addHours(24));
            }

            return $result;
        }

        [$found, $numbersGrouped, $combinations] = LotteryStatistic::statisticSpecial($city, $numbers, $type);

        $result = [
            'found' => $found,
            'numbersGrouped' => $numbersGrouped,
            'cityDates' => $cityDates,
            'numbers' => $numbers,
            'combination' => $combinations->last(),
            'rangeStart' => $rangeStart,
        ];

        // Lưu cache nếu bật (cache 24 giờ)
        if ($useCache) {
            Cache::put($cacheKey, $result, now()->addHours(24));
        }

        return $result;
    }
}
