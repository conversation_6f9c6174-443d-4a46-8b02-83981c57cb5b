<?php

use Bo<PERSON>ble\Base\Facades\AdminHelper;
use Illuminate\Support\Facades\Route;
use Plugin\Lottery\Http\Controllers\LotteryCityController;
use Plugin\Lottery\Http\Controllers\LotteryCityDateController;
use Plugin\Lottery\Http\Controllers\LotteryDTResultController;
use Plugin\Lottery\Http\Controllers\LotteryMBResultController;
use Plugin\Lottery\Http\Controllers\LotteryMNResultController;
use Plugin\Lottery\Http\Controllers\LotteryMTResultController;
use Plugin\Lottery\Http\Controllers\LotteryPostController;
use Plugin\Lottery\Http\Controllers\LotteryRetryNumberController;
use Plugin\Lottery\Http\Controllers\LotteryStatisticController;
use Plugin\Lottery\Http\Controllers\Settings\LotteryPostSettingController;
use Plugin\Lottery\Http\Controllers\Settings\LotterySettingController;

AdminHelper::registerRoutes(function () {
    Route::group([
        'prefix' => 'lotteries',
        'as' => 'lotteries.',
    ], function () {
        Route::group([
            'prefix' => 'mb-results',
            'as' => 'mb-results.',
        ], function () {
            Route::resource('', LotteryMBResultController::class)
                ->parameters(['' => 'result']);
        });

        Route::group([
            'prefix' => 'mt-results',
            'as' => 'mt-results.',
        ], function () {
            Route::resource('', LotteryMTResultController::class)
                ->parameters(['' => 'result']);
        });

        Route::group([
            'prefix' => 'mn-results',
            'as' => 'mn-results.',
        ], function () {
            Route::resource('', LotteryMNResultController::class)
                ->parameters(['' => 'result']);
        });

        Route::group([
            'prefix' => 'dt-results',
            'as' => 'dt-results.',
        ], function () {
            Route::resource('', LotteryDTResultController::class)
                ->parameters(['' => 'result']);
        });

        Route::group([
            'prefix' => 'cities',
            'as' => 'cities.',
        ], function () {
            Route::resource('', LotteryCityController::class)
                ->parameters(['' => 'city']);
        });

        Route::group([
            'prefix' => 'city-dates',
            'as' => 'city-dates.',
        ], function () {
            Route::resource('', LotteryCityDateController::class)
                ->parameters(['' => 'cityDate'])
                ->only(['index', 'edit', 'update']);
        });

        Route::group([
            'prefix' => 'retry-number',
            'as' => 'retry-number',
            'controller' => LotteryRetryNumberController::class
        ], function () {
            Route::get('', 'edit');
            Route::post('', 'update')->name('.update');
        });

        Route::group([
            'prefix' => 'statistics',
            'as' => 'statistics.',
            'controller' => LotteryStatisticController::class
        ], function () {
            Route::get('', 'index')->name('index');
            Route::get('2-diem-duy-nhat-mien-bac', 'twoPointUniqueMB')->name('two-point-unique-mb');
            Route::get('cau-chay-hom-nay', 'twoPointTodayMB')->name('two-point-today-mb');
            Route::get('cau-dac-biet-{type}-{region}', 'special')
                ->name('special')
                ->where('type', 'dau|duoi|tong-dau|tong-duoi|giai-8')
                ->where('region', 'mb|mn|mt');
        });

        Route::group([
            'prefix' => 'posts',
            'as' => 'posts.',
        ], function (): void {
            Route::resource('', LotteryPostController::class)
                ->parameters(['' => 'post']);

            Route::group([
                'prefix' => 'settings',
                'as' => 'settings.',
                'controller' => LotteryPostSettingController::class,
            ], function (): void {
                Route::get('', 'edit')->name('edit');
                Route::put('', 'update')->name('update');
            });
        });
    });

    Route::group([
        'prefix' => 'settings/lottery',
        'as' => 'lotteries.settings',
        'controller' => LotterySettingController::class,
    ], function (): void {
        Route::get('', 'edit');
        Route::put('', 'update')->name('.update');
    });
});
